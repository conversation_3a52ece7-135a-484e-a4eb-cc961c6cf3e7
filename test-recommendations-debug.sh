#!/bin/bash

echo "🔍 Testing Enhanced Recommendations System - Debug Mode"
echo "======================================================"

# JWT Token
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PXBOzvRRyFMnULNQWP74cH8w4sc-WLX7BUCED_DAXn8"

echo -e "\n1️⃣ Testing Enhanced Recommendations Endpoint..."
echo "================================================"

# Test the main recommendations endpoint
curl -X POST http://localhost:3001/recommendations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "problem_description": "I need an AI tool for code documentation",
    "filters": {
      "max_candidates": 20
    }
  }' \
  -o recommendation_response.json \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"

echo -e "\n2️⃣ Analyzing Response..."
echo "========================"

if [ -f "recommendation_response.json" ]; then
  echo "✅ Response received. Analyzing content..."
  
  # Check if response contains recommended_entities
  ENTITY_COUNT=$(jq '.recommended_entities | length' recommendation_response.json 2>/dev/null || echo "0")
  echo "📊 Recommended entities count: $ENTITY_COUNT"
  
  # Check candidates analyzed
  CANDIDATES_ANALYZED=$(jq '.candidates_analyzed' recommendation_response.json 2>/dev/null || echo "0")
  echo "📊 Candidates analyzed: $CANDIDATES_ANALYZED"
  
  # Check LLM provider
  LLM_PROVIDER=$(jq -r '.llm_provider' recommendation_response.json 2>/dev/null || echo "N/A")
  echo "🤖 LLM Provider: $LLM_PROVIDER"
  
  # Check if explanation mentions enhanced features
  EXPLANATION=$(jq -r '.explanation' recommendation_response.json 2>/dev/null || echo "")
  if [[ "$EXPLANATION" == *"diversity"* ]]; then
    echo "✅ Enhanced features detected (diversity mentioned)"
  else
    echo "⚠️ Enhanced features not detected"
  fi
  
  # Check for metadata
  METADATA=$(jq '.metadata' recommendation_response.json 2>/dev/null)
  if [[ "$METADATA" != "null" ]]; then
    echo "✅ Metadata present (enhanced system active)"
    echo "📊 Metadata: $METADATA"
  else
    echo "⚠️ No metadata (possible fallback to basic system)"
  fi
  
  echo -e "\n📄 Full Response:"
  echo "=================="
  jq '.' recommendation_response.json
  
else
  echo "❌ No response file created"
fi

echo -e "\n3️⃣ Testing Debug Endpoint..."
echo "============================="

# Test the debug endpoint
curl -X POST http://localhost:3001/recommendations/debug \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "problem_description": "I need an AI tool for code documentation"
  }' \
  -o debug_response.json \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"

if [ -f "debug_response.json" ]; then
  echo "✅ Debug response received"
  echo -e "\n📄 Debug Response:"
  echo "=================="
  jq '.' debug_response.json
else
  echo "❌ No debug response file created"
fi

echo -e "\n🏁 Test Complete"
echo "================"
