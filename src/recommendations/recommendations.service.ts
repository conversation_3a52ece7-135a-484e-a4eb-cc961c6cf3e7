import { Injectable, Logger, Inject } from '@nestjs/common';
import { EntitiesService } from '../entities/entities.service';
import { ILlmService, CandidateEntity } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationFiltersDto } from './dto/recommendation-filters.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { EntityListItemResponseDto } from '../entities/dto/entity-list-item-response.dto';
import { ListEntitiesDto } from '../entities/dto/list-entities.dto';
import { VectorSearchDto } from '../entities/dto/vector-search.dto';
import { FilterExtractionService } from './services/filter-extraction.service';
import { QueryUnderstandingService } from './services/query-understanding.service';
import { ContextualRerankingService } from './services/contextual-reranking.service';
import { RecommendationFeedbackService } from './services/recommendation-feedback.service';
import { AdvancedEntityRankingService, RankingContext } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../common/performance/query-optimization.service';

@Injectable()
export class RecommendationsService {
  private readonly logger = new Logger(RecommendationsService.name);

  constructor(
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
    private readonly filterExtractionService: FilterExtractionService,
    private readonly queryUnderstandingService: QueryUnderstandingService,
    private readonly contextualRerankingService: ContextualRerankingService,
    private readonly feedbackService: RecommendationFeedbackService,
    private readonly advancedRankingService: AdvancedEntityRankingService,
    private readonly performanceOptimizationService: PerformanceOptimizationService,
    private readonly queryOptimizationService: QueryOptimizationService,
  ) {}

  async getRecommendations(
    createRecommendationDto: CreateRecommendationDto,
  ): Promise<RecommendationResponseDto> {
    const { problem_description, filters } = createRecommendationDto;
    const maxCandidates = filters?.max_candidates || 50; // Increased from 20 to 50 for better coverage

    this.logger.log(
      `Getting enhanced recommendations for problem: "${problem_description}" with max ${maxCandidates} candidates`,
    );

    try {
      // Step 1: Enhanced query understanding and expansion
      const queryAnalysis = await this.queryUnderstandingService.analyzeAndExpandQuery(problem_description);

      this.logger.debug('Query analysis completed:', {
        intent: queryAnalysis.primaryIntent.primary,
        confidence: queryAnalysis.primaryIntent.confidence,
        conceptCount: Object.values(queryAnalysis.concepts).flat().length,
        variantCount: queryAnalysis.searchVariants.length
      });

      // Step 2: Optimized filter extraction with enhanced query context
      const extractedFilters = await this.performanceOptimizationService.optimizedFilterExtraction(
        problem_description,
        () => this.filterExtractionService.extractFiltersFromDescription(problem_description),
      );

      // Step 3: Enhance filters with query analysis insights
      const queryEnhancedFilters = this.enhanceFiltersWithQueryAnalysis(extractedFilters, queryAnalysis);

      // Step 4: Merge explicit filters with enhanced extracted filters (explicit takes precedence)
      const enhancedFilters = this.mergeFilters(queryEnhancedFilters, filters);

      this.logger.debug('Enhanced filters applied:', {
        extractedKeys: Object.keys(extractedFilters),
        queryEnhancedKeys: Object.keys(queryEnhancedFilters),
        explicitKeys: Object.keys(filters || {}),
        finalKeys: Object.keys(enhancedFilters),
      });

      // Step 5: Use enhanced filters and query variants to find candidate entities
      const candidateEntities = await this.findCandidateEntitiesWithQueryVariants(
        problem_description,
        queryAnalysis.searchVariants,
        enhancedFilters,
        maxCandidates,
      );

      this.logger.log(`Found ${candidateEntities.length} candidate entities with enhanced filtering`);

      if (candidateEntities.length === 0) {
        // Implement hybrid fallback strategy
        this.logger.warn('No candidates found with enhanced filtering, trying fallback strategies');

        const fallbackCandidates = await this.getFallbackCandidates(
          problem_description,
          enhancedFilters,
          maxCandidates,
        );

        if (fallbackCandidates.length === 0) {
          return {
            recommended_entities: [],
            explanation: 'No relevant entities found for your query. This might be because:\n' +
                        '• No entities match your specific filters\n' +
                        '• The search terms are too specific\n' +
                        '• No entities have been indexed for vector search yet\n\n' +
                        'Try:\n' +
                        '• Using broader search terms\n' +
                        '• Removing some filters\n' +
                        '• Searching for general categories like "AI tool" or "machine learning"',
            problem_description,
            candidates_analyzed: 0,
            llm_provider: 'N/A',
            generated_at: new Date(),
          };
        }

        // Use fallback candidates
        const llmCandidates = this.convertToLlmCandidates(fallbackCandidates);
        const llmRecommendation = await this.llmService.getRecommendation(
          problem_description,
          llmCandidates,
        );

        const recommendedEntities = await this.getRecommendedEntityDetails(
          llmRecommendation.recommendedEntityIds,
          fallbackCandidates,
        );

        return {
          recommended_entities: recommendedEntities,
          explanation: `${llmRecommendation.explanation}\n\n⚠️ Note: These recommendations were found using broader search criteria since no exact matches were found for your specific query.`,
          problem_description,
          candidates_analyzed: fallbackCandidates.length,
          llm_provider: 'FALLBACK',
          generated_at: new Date(),
        };
      }

      // Step 4: Convert entities to the format expected by LLM service
      const llmCandidates = this.convertToLlmCandidates(candidateEntities);

      // Step 5: Get LLM recommendations with enhanced context
      const llmRecommendation = await this.llmService.getRecommendation(
        problem_description,
        llmCandidates,
      );

      // Step 6: Fetch full entity details for recommended entities
      const recommendedEntities = await this.getRecommendedEntityDetails(
        llmRecommendation.recommendedEntityIds,
        candidateEntities,
      );

      // Step 7: Apply contextual re-ranking for diversity and quality
      const rerankingContext = {
        query: problem_description,
        queryIntent: queryAnalysis.primaryIntent,
        userContext: {
          technicalLevel: queryAnalysis.primaryIntent.techLevel,
          budgetSensitive: queryAnalysis.implicitNeeds.budgetSensitive,
        },
        diversityWeight: this.calculateDiversityWeight(queryAnalysis.primaryIntent),
      };

      const rerankedEntities = await this.contextualRerankingService.rerankResults(
        recommendedEntities,
        rerankingContext,
        10, // Max results
      );

      // Step 8: Get current LLM provider for response metadata
      const currentProvider = await this.getCurrentLlmProvider();

      // Calculate diversity metrics for the final result set
      const diversityScore = this.contextualRerankingService.calculateResultSetDiversity(rerankedEntities);

      return {
        recommended_entities: rerankedEntities,
        explanation: `${llmRecommendation.explanation}\n\n✨ Results optimized for relevance and diversity (diversity score: ${(diversityScore * 100).toFixed(1)}%).`,
        problem_description,
        candidates_analyzed: candidateEntities.length,
        llm_provider: currentProvider,
        generated_at: new Date(),
        // Additional metadata (not in DTO but useful for internal tracking)
        metadata: {
          diversity_score: diversityScore,
          reranking_applied: true,
        },
      };
    } catch (error) {
      this.logger.error('Error generating enhanced recommendations', error.stack);
      throw error;
    }
  }

  private async findCandidateEntities(
    problemDescription: string,
    filters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    this.logger.debug(`Finding candidates with filters:`, {
      filterKeys: Object.keys(filters || {}),
      maxCandidates,
    });

    // Step 1: Use vector search to find semantically relevant entities
    const vectorResults = await this.entitiesService.vectorSearch({
      query: problemDescription,
      limit: maxCandidates * 4, // Increased multiplier from 3 to 4 for better filtering options
    });

    if (vectorResults.length === 0) {
      this.logger.warn('No vector search results found');
      return [];
    }

    this.logger.debug(`Vector search found ${vectorResults.length} candidates`);

    // Step 2: Extract entity IDs from vector search results
    const entityIds = vectorResults.map(result => result.id);

    // Step 3: Fetch the specific entities found by vector search
    // This is the key fix - we need to get only the entities from vector search, not all entities
    const queryStartTime = Date.now();

    // Fetch entities individually since there's no findByIds method
    const vectorEntities = await Promise.all(
      entityIds.map(async (id) => {
        try {
          return await this.entitiesService.findOne(id);
        } catch (error) {
          this.logger.warn(`Failed to fetch entity ${id}:`, error.message);
          return null;
        }
      })
    );

    // Filter out null results
    const validVectorEntities = vectorEntities.filter(entity => entity !== null);
    const queryTime = Date.now() - queryStartTime;

    this.logger.debug(`Fetched ${validVectorEntities.length} entities from vector search results (${entityIds.length} requested)`);

    // Step 4: Apply filters to the vector search results
    const filteredEntities = this.applyFiltersToEntities(validVectorEntities, filters);

    this.logger.debug(`After applying filters: ${filteredEntities.length} entities remain`);

    // Step 5: Maintain vector search relevance order
    // Sort the filtered entities by their original vector search order
    const orderedEntities = entityIds
      .map(id => filteredEntities.find(entity => entity.id === id))
      .filter(entity => entity !== undefined);

    // Step 6: Apply optimized advanced ranking
    const rankingContext: RankingContext = {
      appliedFilters: filters as any, // Use the original filters for ranking context
      filterConfidence: this.extractFilterConfidence(filters),
      userPreferences: this.extractUserPreferences(filters),
      currentResults: [],
    };

    const rankedEntities = await this.performanceOptimizationService.optimizedEntityRanking(
      orderedEntities,
      rankingContext,
      () => this.advancedRankingService.rankEntities(orderedEntities, rankingContext),
    );

    const finalEntities = rankedEntities.slice(0, maxCandidates);

    this.logger.debug(`Advanced ranking completed`, {
      vectorSearchCount: vectorEntities.length,
      filteredCount: filteredEntities.length,
      orderedCount: orderedEntities.length,
      finalCount: finalEntities.length,
      topScore: finalEntities[0]?.rankingScore,
      avgScore: finalEntities.length > 0 ? finalEntities.reduce((sum, e) => sum + (e.rankingScore || 0), 0) / finalEntities.length : 0,
    });

    return finalEntities;
  }

  /**
   * Merge extracted filters with explicit filters, giving precedence to explicit filters
   */
  private mergeFilters(
    extractedFilters: Partial<RecommendationFiltersDto>,
    explicitFilters?: RecommendationFiltersDto,
  ): RecommendationFiltersDto {
    const merged: RecommendationFiltersDto = {
      ...extractedFilters,
      ...explicitFilters, // Explicit filters override extracted ones
    };

    // Special handling for array fields - combine rather than override
    if (extractedFilters.entityTypeIds && explicitFilters?.entityTypeIds) {
      merged.entityTypeIds = [...new Set([...extractedFilters.entityTypeIds, ...explicitFilters.entityTypeIds])];
    }

    if (extractedFilters.technical_levels && explicitFilters?.technical_levels) {
      merged.technical_levels = [...new Set([...extractedFilters.technical_levels, ...explicitFilters.technical_levels])];
    }

    if (extractedFilters.platforms && explicitFilters?.platforms) {
      merged.platforms = [...new Set([...extractedFilters.platforms, ...explicitFilters.platforms])];
    }

    if (extractedFilters.frameworks && explicitFilters?.frameworks) {
      merged.frameworks = [...new Set([...extractedFilters.frameworks, ...explicitFilters.frameworks])];
    }

    return merged;
  }

  /**
   * Extract filter confidence scores for ranking
   */
  private extractFilterConfidence(filters: RecommendationFiltersDto): Record<string, number> {
    const confidence: Record<string, number> = {};

    // High confidence for explicit filters
    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== null) {
        confidence[key] = 0.9; // High confidence for explicit filters
      }
    });

    return confidence;
  }

  /**
   * Extract user preferences for ranking context
   */
  private extractUserPreferences(filters: RecommendationFiltersDto): any {
    return {
      technical_level: filters.technical_levels?.[0],
      budget: filters.has_free_tier ? 'free' :
              filters.price_range === 'LOW' ? 'low' :
              filters.price_range === 'MEDIUM' ? 'medium' : 'high',
      preferred_categories: [], // Could be enhanced with user profile data
      excluded_categories: [],
    };
  }

  /**
   * Generate a signature for query performance tracking
   */
  private generateQuerySignature(filters: ListEntitiesDto): string {
    const keyFilters = {
      entityTypes: filters.entityTypeIds?.length || 0,
      hasSearch: !!filters.searchTerm,
      categories: filters.categoryIds?.length || 0,
      techLevels: filters.technical_levels?.length || 0,
      hasFreeTier: filters.has_free_tier,
      hasApi: filters.has_api,
      priceRange: filters.price_range ? 1 : 0,
    };

    return `query:${JSON.stringify(keyFilters)}`;
  }

  private convertToLlmCandidates(entities: any[]): CandidateEntity[] {
    return entities.map((entity) => ({
      id: entity.id,
      name: entity.name,
      shortDescription: entity.shortDescription,
      description: entity.description,
      entityType: {
        name: entity.entityType.name,
        slug: entity.entityType.slug,
      },
      categories: entity.entityCategories || [],
      tags: entity.entityTags || [],
      features: entity.entityFeatures || [],
      websiteUrl: entity.websiteUrl,
      logoUrl: entity.logoUrl,
      avgRating: entity.avgRating,
      reviewCount: entity.reviewCount,
    }));
  }

  private async getRecommendedEntityDetails(
    recommendedIds: string[],
    candidateEntities: any[],
  ): Promise<EntityListItemResponseDto[]> {
    // Filter candidate entities to only include recommended ones, maintaining order
    const recommendedEntities = recommendedIds
      .map((id) => candidateEntities.find((entity) => entity.id === id))
      .filter((entity) => entity !== undefined);

    // Convert to response DTOs (you may need to implement this mapping)
    return recommendedEntities.map((entity) => this.mapToEntityListItemResponseDto(entity));
  }

  private mapToEntityListItemResponseDto(entity: any): EntityListItemResponseDto {
    // This should match the mapping logic from EntitiesController
    const listItemDto = new EntityListItemResponseDto();
    listItemDto.id = entity.id;
    listItemDto.name = entity.name;
    listItemDto.slug = entity.slug;
    listItemDto.logoUrl = entity.logoUrl;
    listItemDto.shortDescription = entity.shortDescription;
    listItemDto.websiteUrl = entity.websiteUrl;
    listItemDto.entityType = {
      name: entity.entityType.name,
      slug: entity.entityType.slug,
    };
    listItemDto.avgRating = entity.avgRating;
    listItemDto.reviewCount = entity.reviewCount;
    listItemDto.saveCount = entity._count?.userSavedEntities ?? 0;

    if (entity.entityType?.slug === 'ai-tool' && entity.entityDetailsTool) {
      listItemDto.hasFreeTier = entity.entityDetailsTool.hasFreeTier;
    }

    return listItemDto;
  }

  private async getCurrentLlmProvider(): Promise<string> {
    try {
      // Get the current provider from the factory service
      const providers = this.llmFactoryService.getAvailableProviders();
      // For now, we'll determine the provider by checking which service is being used
      // In a more sophisticated implementation, you might want to expose this from the factory
      return 'OPENAI'; // Default assumption, could be enhanced
    } catch (error) {
      this.logger.warn('Could not determine current LLM provider', error.message);
      return 'UNKNOWN';
    }
  }

  /**
   * Hybrid fallback strategy when no candidates are found
   */
  private async getFallbackCandidates(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    this.logger.debug('Executing fallback candidate search strategies');

    // Strategy 1: Try vector search with lower threshold
    let candidates = await this.tryLowerThresholdVectorSearch(problemDescription, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 1 (lower threshold): Found ${candidates.length} candidates`);
      return candidates;
    }

    // Strategy 2: Try without entity type filters
    if (originalFilters.entityTypeIds?.length || originalFilters.entity_type_ids?.length) {
      candidates = await this.tryWithoutEntityTypeFilters(problemDescription, originalFilters, maxCandidates);
      if (candidates.length > 0) {
        this.logger.debug(`Fallback Strategy 2 (no entity type filter): Found ${candidates.length} candidates`);
        return candidates;
      }
    }

    // Strategy 3: Try keyword-based search instead of vector search
    candidates = await this.tryKeywordBasedSearch(problemDescription, originalFilters, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 3 (keyword search): Found ${candidates.length} candidates`);
      return candidates;
    }

    // Strategy 4: Try with minimal filters (only status and basic criteria)
    candidates = await this.tryMinimalFilters(problemDescription, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 4 (minimal filters): Found ${candidates.length} candidates`);
      return candidates;
    }

    this.logger.warn('All fallback strategies failed to find candidates');
    return [];
  }

  /**
   * Strategy 1: Try vector search with lower similarity threshold
   */
  private async tryLowerThresholdVectorSearch(problemDescription: string, maxCandidates: number): Promise<any[]> {
    try {
      // For now, we'll use the regular vector search since we don't have vectorSearchWithThreshold
      // But we can implement keyword-based fallback
      const vectorResults = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: maxCandidates * 5, // Get more results to increase chances
      });

      if (vectorResults.length === 0) {
        return [];
      }

      const entityIds = vectorResults.map(result => result.id);
      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        status: 'ACTIVE',
      });

      return entityIds
        .map(id => result.data.find(entity => entity.id === id))
        .filter(entity => entity !== undefined)
        .slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Lower threshold vector search failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 2: Try without entity type filters
   */
  private async tryWithoutEntityTypeFilters(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    try {
      const vectorResults = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: maxCandidates * 3,
      });

      if (vectorResults.length === 0) {
        return [];
      }

      const entityIds = vectorResults.map(result => result.id);
      const filtersWithoutEntityType = { ...originalFilters };
      delete filtersWithoutEntityType.entityTypeIds;
      delete filtersWithoutEntityType.entity_type_ids;

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        ...filtersWithoutEntityType,
      });

      return entityIds
        .map(id => result.data.find(entity => entity.id === id))
        .filter(entity => entity !== undefined)
        .slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Search without entity type filters failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 3: Try keyword-based search instead of vector search
   */
  private async tryKeywordBasedSearch(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    try {
      // Extract keywords from the problem description
      const keywords = this.extractKeywords(problemDescription);

      if (keywords.length === 0) {
        return [];
      }

      // Use the most relevant keyword as searchTerm
      const searchTerm = keywords[0];

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        searchTerm,
        status: 'ACTIVE',
        ...originalFilters,
      });

      return result.data.slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Keyword-based search failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 4: Try with minimal filters (only status and basic criteria)
   */
  private async tryMinimalFilters(problemDescription: string, maxCandidates: number): Promise<any[]> {
    try {
      // Extract basic entity type from description
      const extractedFilters = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        status: 'ACTIVE',
        entityTypeIds: extractedFilters.entityTypeIds, // Only use extracted entity types
      });

      return result.data.slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Minimal filters search failed', error.message);
      return [];
    }
  }

  /**
   * Extract keywords from problem description for fallback search
   */
  private extractKeywords(description: string): string[] {
    const stopWords = new Set(['i', 'need', 'want', 'looking', 'for', 'to', 'help', 'me', 'with', 'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'by', 'from']);

    return description
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 5); // Take top 5 keywords
  }

  /**
   * Debug method to check recommendation pipeline
   */
  async debugRecommendationPipeline(problemDescription: string): Promise<any> {
    const debug: any = {
      step1_embedding_generation: null,
      step2_vector_search: null,
      step3_embedding_coverage: null,
      step4_filter_extraction: null,
      step5_basic_search: null,
    };

    try {
      // Step 1: Check if we can generate embeddings
      debug.step1_embedding_generation = await this.entitiesService.debugEmbeddingGeneration(problemDescription);

      // Step 2: Check vector search
      debug.step2_vector_search = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: 10,
      });

      // Step 3: Check embedding coverage
      debug.step3_embedding_coverage = await this.entitiesService.getEmbeddingCoverage();

      // Step 4: Check filter extraction
      debug.step4_filter_extraction = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);

      // Step 5: Check basic search without filters
      const basicSearch = await this.entitiesService.findAll({
        limit: 10,
        page: 1,
        status: 'ACTIVE',
      });
      debug.step5_basic_search = {
        total_entities: basicSearch.total,
        returned_entities: basicSearch.data.length,
        sample_entities: basicSearch.data.slice(0, 3).map(e => ({ id: e.id, name: e.name, entityTypeId: e.entityTypeId })),
      };

      return debug;
    } catch (error) {
      return {
        ...debug,
        error: error.message,
        stack: error.stack,
      };
    }
  }

  /**
   * Enhance extracted filters with insights from query analysis
   */
  private enhanceFiltersWithQueryAnalysis(
    extractedFilters: Partial<RecommendationFiltersDto>,
    queryAnalysis: any, // Import the type from query-understanding.service
  ): Partial<RecommendationFiltersDto> {
    const enhanced = { ...extractedFilters };

    // Add entity types from query analysis if not already present
    if (!enhanced.entityTypeIds && queryAnalysis.concepts.entityTypes.length > 0) {
      // Map concept entity types to actual entity type IDs
      // This would need to be implemented based on your entity type mapping
      enhanced.entity_type_ids = queryAnalysis.concepts.entityTypes;
    }

    // Add technology-based filters
    if (queryAnalysis.concepts.technologies.length > 0) {
      const techTerms = queryAnalysis.concepts.technologies.join(' ');
      enhanced.key_features_search = enhanced.key_features_search
        ? `${enhanced.key_features_search} ${techTerms}`
        : techTerms;
    }

    // Add use case filters
    if (queryAnalysis.concepts.useCases.length > 0) {
      const useCaseTerms = queryAnalysis.concepts.useCases.join(' ');
      enhanced.use_cases_search = enhanced.use_cases_search
        ? `${enhanced.use_cases_search} ${useCaseTerms}`
        : useCaseTerms;
    }

    // Apply implicit needs as filters
    if (queryAnalysis.implicitNeeds.budgetSensitive) {
      // Only set if not already set, and convert to string format expected by DTO
      if (!enhanced.price_range) {
        enhanced.price_range = 'FREE,FREEMIUM';
      }
    }

    if (queryAnalysis.implicitNeeds.easeOfUse && queryAnalysis.primaryIntent.techLevel === 'beginner') {
      enhanced.technical_levels = enhanced.technical_levels || ['BEGINNER', 'INTERMEDIATE'];
    }

    return enhanced;
  }

  /**
   * Find candidate entities using multiple query variants for better coverage
   */
  private async findCandidateEntitiesWithQueryVariants(
    originalQuery: string,
    queryVariants: string[],
    filters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    this.logger.debug(`Finding candidates with ${queryVariants.length} query variants`);

    // Try the original query first
    let candidateEntities = await this.findCandidateEntities(
      originalQuery,
      filters,
      maxCandidates,
    );

    // If we have enough candidates, return them
    if (candidateEntities.length >= Math.min(maxCandidates * 0.7, 20)) {
      this.logger.debug(`Original query found sufficient candidates: ${candidateEntities.length}`);
      return candidateEntities;
    }

    // Try query variants to get more candidates
    const allCandidates = new Map<string, any>();

    // Add original results
    candidateEntities.forEach(entity => allCandidates.set(entity.id, entity));

    // Try each variant (limit to prevent excessive API calls)
    const variantsToTry = queryVariants.slice(1, 4); // Skip original, try up to 3 variants

    for (const variant of variantsToTry) {
      if (allCandidates.size >= maxCandidates) break;

      this.logger.debug(`Trying query variant: "${variant}"`);

      try {
        const variantCandidates = await this.findCandidateEntities(
          variant,
          filters,
          Math.max(10, maxCandidates - allCandidates.size),
        );

        // Add new candidates
        variantCandidates.forEach(entity => {
          if (!allCandidates.has(entity.id)) {
            allCandidates.set(entity.id, entity);
          }
        });

        this.logger.debug(`Variant "${variant}" added ${variantCandidates.length} candidates, total: ${allCandidates.size}`);

      } catch (error) {
        this.logger.warn(`Query variant "${variant}" failed:`, error.message);
      }
    }

    const finalCandidates = Array.from(allCandidates.values()).slice(0, maxCandidates);

    this.logger.log(`Query variants search completed: ${finalCandidates.length} total candidates from ${queryVariants.length} variants`);

    return finalCandidates;
  }

  /**
   * Calculate appropriate diversity weight based on query intent
   */
  private calculateDiversityWeight(queryIntent: any): number {
    // Higher diversity for exploration queries
    if (queryIntent.primary === 'exploration') {
      return 0.4;
    }

    // Lower diversity for specific needs
    if (queryIntent.primary === 'specific_need') {
      return 0.2;
    }

    // Medium diversity for comparison queries
    if (queryIntent.primary === 'comparison') {
      return 0.3;
    }

    // Default balanced diversity
    return 0.25;
  }

  /**
   * Track user interaction with recommendations
   */
  async trackRecommendationInteraction(
    userId: string | undefined,
    sessionId: string,
    query: string,
    recommendedEntityIds: string[],
    clickedEntityId?: string,
    position?: number,
    dwellTime?: number,
    userAgent?: string,
  ): Promise<void> {
    try {
      await this.feedbackService.trackInteraction({
        userId,
        sessionId,
        query,
        recommendedEntityIds,
        clickedEntityId,
        position,
        dwellTime,
        userAgent,
        timestamp: new Date(),
      });

      this.logger.debug(`Tracked recommendation interaction for query: "${query}"`);

    } catch (error) {
      this.logger.error('Error tracking recommendation interaction:', error.stack);
      // Don't throw to avoid breaking the main flow
    }
  }

  /**
   * Get personalized recommendations based on user history
   */
  async getPersonalizedRecommendations(
    createRecommendationDto: CreateRecommendationDto,
    userId?: string,
  ): Promise<RecommendationResponseDto> {
    // Get base recommendations
    const baseRecommendations = await this.getRecommendations(createRecommendationDto);

    // Apply personalization if user is logged in
    if (userId && baseRecommendations.recommended_entities.length > 0) {
      try {
        // Get personalized boosts for each entity
        const personalizedEntities = await Promise.all(
          baseRecommendations.recommended_entities.map(async (entity) => {
            const personalizedBoost = await this.feedbackService.getPersonalizedBoost(userId, entity.id);
            return {
              ...entity,
              personalizedScore: (entity.avgRating || 2.5) * personalizedBoost,
              personalizedBoost,
            };
          })
        );

        // Re-sort by personalized score
        personalizedEntities.sort((a, b) => (b.personalizedScore || 0) - (a.personalizedScore || 0));

        return {
          ...baseRecommendations,
          recommended_entities: personalizedEntities,
          explanation: `${baseRecommendations.explanation}\n\n🎯 Results personalized based on your preferences and interaction history.`,
          metadata: {
            ...baseRecommendations.metadata,
            personalization_applied: true,
          },
        };

      } catch (error) {
        this.logger.warn('Error applying personalization, returning base recommendations:', error.message);
      }
    }

    return baseRecommendations;
  }

  /**
   * Get recommendation quality metrics
   */
  async getQualityMetrics(timeWindowDays: number = 7): Promise<any> {
    try {
      return await this.feedbackService.getQualityMetrics(timeWindowDays);
    } catch (error) {
      this.logger.error('Error getting quality metrics:', error.stack);
      return {
        clickThroughRate: 0,
        averagePosition: 0,
        zeroClickRate: 0,
        averageDwellTime: 0,
        totalInteractions: 0,
      };
    }
  }

  /**
   * Apply filters to a list of entities (client-side filtering)
   */
  private applyFiltersToEntities(entities: any[], filters: RecommendationFiltersDto): any[] {
    if (!filters || Object.keys(filters).length === 0) {
      return entities;
    }

    return entities.filter(entity => {
      // Entity type filtering
      if (filters.entityTypeIds?.length || filters.entity_type_ids?.length) {
        const allowedTypes = [...(filters.entityTypeIds || []), ...(filters.entity_type_ids || [])];
        if (!allowedTypes.includes(entity.entityTypeId)) {
          return false;
        }
      }

      // Status filtering
      if (filters.status && entity.status !== filters.status) {
        return false;
      }

      // Search term filtering (basic implementation)
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const entityText = `${entity.name} ${entity.shortDescription || ''} ${entity.description || ''}`.toLowerCase();
        if (!entityText.includes(searchTerm)) {
          return false;
        }
      }

      // Category filtering
      if (filters.categoryIds?.length) {
        const entityCategoryIds = entity.entityCategories?.map((ec: { categoryId: string }) => ec.categoryId) || [];
        const hasMatchingCategory = filters.categoryIds.some(catId => entityCategoryIds.includes(catId));
        if (!hasMatchingCategory) {
          return false;
        }
      }

      // Tag filtering
      if (filters.tagIds?.length) {
        const entityTagIds = entity.entityTags?.map((et: { tagId: string }) => et.tagId) || [];
        const hasMatchingTag = filters.tagIds.some(tagId => entityTagIds.includes(tagId));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // Feature filtering
      if (filters.featureIds?.length) {
        const entityFeatureIds = entity.entityFeatures?.map((ef: { featureId: string }) => ef.featureId) || [];
        const hasMatchingFeature = filters.featureIds.some(featId => entityFeatureIds.includes(featId));
        if (!hasMatchingFeature) {
          return false;
        }
      }

      // Add more specific filters as needed
      // For now, we'll pass through entities that match the basic filters
      return true;
    });
  }
}
